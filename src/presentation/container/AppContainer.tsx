import React, { FunctionComponent } from 'react';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';

import { AppNavigator } from './AppNavigator';

const Routes = () => {
  return (
    <Switch>
      {/* Main app route */}
      <Route path="/" component={AppNavigator} />
    </Switch>
  );
};

const AppContainer: FunctionComponent<any> = () => {
  const baseName = window.__BASE_NAME__ || '/spa/v2';
  return (
    <Router basename={`${baseName}`}>
      <Routes />
    </Router>
  );
};

export default AppContainer;
