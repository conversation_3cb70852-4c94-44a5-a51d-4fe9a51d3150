import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { PATH } from '@share/constant/path';
import { Product, CartItem, PaymentMethod } from '../../types';

// Import page components
import OnboardingPage from '@presentation/page/OnboardingPage';
import HomePage from '@presentation/page/HomePage';
import DetailPage from '@presentation/page/DetailPage';
import CartPage from '@presentation/page/CartPage';
import PaymentPage from '@presentation/page/PaymentPage';
import SearchPage from '@presentation/page/SearchPage';
import SearchNotFoundPage from '@presentation/page/SearchNotFoundPage';

export const AppNavigator: React.FC = () => {
  const history = useHistory();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // Navigation handlers
  const handleGetStarted = () => {
    history.push(PATH.HOME);
  };

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    history.push(PATH.DETAIL);
  };

  const handleSearchClick = () => {
    history.push(PATH.SEARCH);
  };

  const handleBack = () => {
    history.goBack();
  };

  const handleAddToCart = (product: Product, quantity: number) => {
    const existingItem = cartItems.find(item => item.id === product.id);
    
    if (existingItem) {
      setCartItems(cartItems.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + quantity }
          : item
      ));
    } else {
      setCartItems([...cartItems, {
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        quantity
      }]);
    }
    
    // Navigate to cart
    history.push(PATH.CART);
  };

  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    if (quantity === 0) {
      setCartItems(cartItems.filter(item => item.id !== itemId));
    } else {
      setCartItems(cartItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      ));
    }
  };

  const handleRemoveItem = (itemId: string) => {
    setCartItems(cartItems.filter(item => item.id !== itemId));
  };

  const handleCheckout = (_items: CartItem[], _total: number) => {
    history.push(PATH.PAYMENT);
  };

  const handlePaymentComplete = (method: PaymentMethod, amount: number) => {
    // Clear cart and navigate to home
    setCartItems([]);
    alert(`Payment of ₦${amount.toLocaleString()} completed successfully with ${method.name}!`);
    history.push(PATH.HOME);
  };

  const handleSearch = (query: string) => {
    // For demo purposes, simulate search results
    if (query.toLowerCase().includes('pizza') || query.toLowerCase().includes('burger')) {
      history.push(PATH.SEARCH);
    } else {
      history.push(PATH.SEARCH_NOT_FOUND);
    }
  };

  // Get current path to determine which component to render
  const currentPath = history.location.pathname;

  // Render appropriate component based on current path
  if (currentPath.includes('onboarding')) {
    return <OnboardingPage onGetStarted={handleGetStarted} />;
  }
  
  if (currentPath.includes('home')) {
    return (
      <HomePage 
        onProductClick={handleProductClick}
        onSearchClick={handleSearchClick}
      />
    );
  }
  
  if (currentPath.includes('detail')) {
    return (
      <DetailPage 
        product={selectedProduct || undefined}
        onBack={handleBack}
        onAddToCart={handleAddToCart}
      />
    );
  }
  
  if (currentPath.includes('cart')) {
    return (
      <CartPage 
        items={cartItems}
        onBack={handleBack}
        onUpdateQuantity={handleUpdateQuantity}
        onRemoveItem={handleRemoveItem}
        onCheckout={handleCheckout}
      />
    );
  }
  
  if (currentPath.includes('payment')) {
    return (
      <PaymentPage 
        onBack={handleBack}
        onPaymentComplete={handlePaymentComplete}
      />
    );
  }
  
  if (currentPath.includes('search-not-found')) {
    return (
      <SearchNotFoundPage 
        onBack={handleBack}
        onSearch={handleSearch}
      />
    );
  }
  
  if (currentPath.includes('search')) {
    return (
      <SearchPage 
        onBack={handleBack}
        onProductClick={handleProductClick}
      />
    );
  }

  // Default to onboarding
  return <OnboardingPage onGetStarted={handleGetStarted} />;
};
