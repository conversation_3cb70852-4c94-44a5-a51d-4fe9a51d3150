import React, { useState } from 'react';
import { useResponsive } from '../../hooks/useResponsive';
import { PaymentMethod } from '../../types';

interface PaymentPageProps {
  total?: number;
  onBack?: () => void;
  onPaymentComplete?: (method: PaymentMethod, amount: number) => void;
}

const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'card',
    type: 'card',
    name: 'Card',
    details: 'Visa, Mastercard, Verve',
    icon: '💳'
  },
  {
    id: 'bank',
    type: 'bank',
    name: 'Bank account',
    details: 'Direct bank transfer',
    icon: '🏦'
  },
  {
    id: 'door-delivery',
    type: 'wallet',
    name: 'Door delivery',
    details: 'Pay on delivery',
    icon: '🚪'
  }
];

const PaymentPage: React.FC<PaymentPageProps> = ({
  total = 23000,
  onBack,
  onPaymentComplete
}) => {
  const { isMobile } = useResponsive();
  const [selectedMethod, setSelectedMethod] = useState<string>('card');

  const handlePayment = () => {
    const method = PAYMENT_METHODS.find(m => m.id === selectedMethod);
    if (method) {
      onPaymentComplete?.(method, total);
    }
  };

  return (
    <div className="screen-container bg-gray-50">
      {/* Header */}
      <div className="safe-area-top bg-gray-50">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <button 
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <h1 className="text-xl font-bold text-gray-900">Checkout</h1>
            
            <div className="w-10" /> {/* Spacer */}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-6">
        {/* Payment Methods Section */}
        <div className="mb-8">
          <h2 className="text-lg font-bold text-gray-900 mb-6">Payment method</h2>
          
          <div className="space-y-4">
            {PAYMENT_METHODS.map((method) => (
              <button
                key={method.id}
                onClick={() => setSelectedMethod(method.id)}
                className="w-full card hover:shadow-lg transition-shadow duration-200 text-left"
              >
                <div className="flex items-center space-x-4">
                  {/* Payment Method Icon */}
                  <div className="w-12 h-12 rounded-xl bg-orange-500 flex items-center justify-center text-white text-xl flex-shrink-0">
                    {method.icon}
                  </div>

                  {/* Payment Method Details */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900">
                      {method.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {method.details}
                    </p>
                  </div>

                  {/* Radio Button */}
                  <div className="flex-shrink-0">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      selectedMethod === method.id
                        ? 'border-red-primary bg-red-primary'
                        : 'border-gray-300'
                    }`}>
                      {selectedMethod === method.id && (
                        <div className="w-2 h-2 bg-white rounded-full" />
                      )}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Delivery Method Section */}
        <div className="mb-8">
          <h2 className="text-lg font-bold text-gray-900 mb-6">Delivery method</h2>
          
          <div className="card">
            <div className="flex items-center space-x-4">
              {/* Delivery Icon */}
              <div className="w-12 h-12 rounded-xl bg-pink-500 flex items-center justify-center text-white text-xl flex-shrink-0">
                🚚
              </div>

              {/* Delivery Details */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900">
                  Door delivery
                </h3>
                <p className="text-sm text-gray-600">
                  25 - 30 minutes
                </p>
              </div>

              {/* Radio Button */}
              <div className="flex-shrink-0">
                <div className="w-6 h-6 rounded-full border-2 border-red-primary bg-red-primary flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="bg-white rounded-t-3xl px-6 py-6">
        {/* Total */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <span className="text-lg font-semibold text-gray-900">Total</span>
            <span className="text-2xl font-bold text-red-primary">₦{total.toLocaleString()}</span>
          </div>
        </div>

        {/* Proceed Button */}
        <button
          onClick={handlePayment}
          className="w-full btn-primary text-center py-4 text-lg font-semibold"
        >
          Proceed to payment
        </button>
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom bg-white" />
    </div>
  );
};

export default PaymentPage;
