import React, { useState } from 'react';
import { useResponsive } from '../../hooks/useResponsive';
import { CartItem } from '../../types';

interface CartPageProps {
  items?: CartItem[];
  onBack?: () => void;
  onUpdateQuantity?: (itemId: string, quantity: number) => void;
  onRemoveItem?: (itemId: string) => void;
  onCheckout?: (items: CartItem[], total: number) => void;
}

const SAMPLE_CART_ITEMS: CartItem[] = [
  {
    id: '1',
    name: 'Veggie tomato mix',
    price: 1900,
    image: '/api/placeholder/150/150',
    quantity: 1
  },
  {
    id: '2',
    name: 'Veggie tomato mix',
    price: 1900,
    image: '/api/placeholder/150/150',
    quantity: 1
  }
];

const CartPage: React.FC<CartPageProps> = ({
  items = SAMPLE_CART_ITEMS,
  onBack,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout
}) => {
  const { isMobile } = useResponsive();
  const [cartItems, setCartItems] = useState<CartItem[]>(items);

  const handleQuantityChange = (itemId: string, change: number) => {
    const updatedItems = cartItems.map(item => {
      if (item.id === itemId) {
        const newQuantity = Math.max(0, item.quantity + change);
        if (newQuantity === 0) {
          onRemoveItem?.(itemId);
          return null;
        }
        onUpdateQuantity?.(itemId, newQuantity);
        return { ...item, quantity: newQuantity };
      }
      return item;
    }).filter(Boolean) as CartItem[];
    
    setCartItems(updatedItems);
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = 1100;
  const total = subtotal + deliveryFee;

  const handleCheckout = () => {
    onCheckout?.(cartItems, total);
  };

  if (cartItems.length === 0) {
    return (
      <div className="screen-container bg-gray-50">
        <div className="safe-area-top bg-gray-50">
          <div className="px-6 py-4">
            <button 
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center px-6">
          <div className="text-center">
            <div className="text-6xl mb-4">🛒</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
            <p className="text-gray-600">Add some delicious items to get started!</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="screen-container bg-gray-50">
      {/* Header */}
      <div className="safe-area-top bg-gray-50">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <button 
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <h1 className="text-xl font-bold text-gray-900">Cart</h1>
            
            <div className="w-10" /> {/* Spacer */}
          </div>
        </div>
      </div>

      {/* Cart Items */}
      <div className="flex-1 px-6 py-4 space-y-4">
        {cartItems.map((item) => (
          <div key={item.id} className="card">
            <div className="flex items-center space-x-4">
              {/* Product Image */}
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-orange-200 to-red-200 flex items-center justify-center flex-shrink-0">
                <span className="text-2xl">🍅</span>
              </div>

              {/* Product Details */}
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 truncate">
                  {item.name}
                </h3>
                <p className="text-red-primary font-bold text-lg">
                  ₦{item.price.toLocaleString()}
                </p>
              </div>

              {/* Quantity Controls */}
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => handleQuantityChange(item.id, -1)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
                
                <span className="text-lg font-semibold text-gray-900 min-w-[1.5rem] text-center">
                  {item.quantity}
                </span>
                
                <button
                  onClick={() => handleQuantityChange(item.id, 1)}
                  className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Order Summary */}
      <div className="bg-white rounded-t-3xl px-6 py-6 space-y-4">
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-semibold text-gray-900">₦{subtotal.toLocaleString()}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Delivery fee</span>
            <span className="font-semibold text-gray-900">₦{deliveryFee.toLocaleString()}</span>
          </div>
          
          <div className="border-t border-gray-200 pt-3">
            <div className="flex justify-between items-center">
              <span className="text-lg font-bold text-gray-900">Total</span>
              <span className="text-xl font-bold text-red-primary">₦{total.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Checkout Button */}
        <button
          onClick={handleCheckout}
          className="w-full btn-primary text-center py-4 text-lg font-semibold mt-6"
        >
          Checkout
        </button>
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom bg-white" />
    </div>
  );
};

export default CartPage;
