import React, { useState } from 'react';
import { useResponsive } from '../../hooks/useResponsive';

interface SearchNotFoundPageProps {
  onBack?: () => void;
  onSearch?: (query: string) => void;
  searchQuery?: string;
}

const SUGGESTED_SEARCHES = [
  'Pizza',
  'Burger', 
  'Salad',
  'Pasta',
  'Sushi',
  'Tacos',
  'Chicken',
  'Vegetarian'
];

const SearchNotFoundPage: React.FC<SearchNotFoundPageProps> = ({
  onBack,
  onSearch,
  searchQuery = ''
}) => {
  const { isMobile } = useResponsive();
  const [newSearchQuery, setNewSearchQuery] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (newSearchQuery.trim()) {
      onSearch?.(newSearchQuery.trim());
    }
  };

  const handleSuggestedSearch = (suggestion: string) => {
    setNewSearchQuery(suggestion);
    onSearch?.(suggestion);
  };

  return (
    <div className="screen-container bg-gray-100">
      {/* Header */}
      <div className="safe-area-top bg-gray-100">
        <div className="px-6 py-4">
          <div className="flex items-center space-x-4 mb-6">
            <button 
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-200 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Search Input */}
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  value={newSearchQuery}
                  onChange={(e) => setNewSearchQuery(e.target.value)}
                  placeholder="Search"
                  className="w-full bg-white rounded-3xl px-6 py-4 pr-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary/20"
                />
                <button
                  type="submit"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2"
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-6 text-center">
        {/* Large Search Icon */}
        <div className="mb-8">
          <div className="w-32 h-32 mx-auto mb-6 flex items-center justify-center">
            <svg 
              className="w-24 h-24 text-gray-300" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              strokeWidth={1.5}
            >
              <circle cx="11" cy="11" r="8" />
              <path d="m21 21-4.35-4.35" />
            </svg>
          </div>
        </div>

        {/* Not Found Message */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Item not found
          </h2>
          <p className="text-gray-600 max-w-sm leading-relaxed">
            Try searching the item with a different keyword.
          </p>
          {searchQuery && (
            <p className="text-gray-500 text-sm mt-2">
              No results for "{searchQuery}"
            </p>
          )}
        </div>

        {/* Suggested Searches */}
        <div className="w-full max-w-sm">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Try searching for:
          </h3>
          <div className="grid grid-cols-2 gap-3">
            {SUGGESTED_SEARCHES.map((suggestion) => (
              <button
                key={suggestion}
                onClick={() => handleSuggestedSearch(suggestion)}
                className="px-4 py-3 bg-white rounded-xl text-gray-700 font-medium hover:bg-gray-50 hover:shadow-md transition-all duration-200 text-left"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>

        {/* Alternative Actions */}
        <div className="mt-8 space-y-3">
          <button
            onClick={() => setNewSearchQuery('')}
            className="text-red-primary font-semibold hover:text-red-secondary transition-colors duration-200"
          >
            Clear search
          </button>
          <div className="text-gray-400">or</div>
          <button
            onClick={onBack}
            className="text-gray-600 font-medium hover:text-gray-800 transition-colors duration-200"
          >
            Go back to browse
          </button>
        </div>
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom bg-gray-100" />
    </div>
  );
};

export default SearchNotFoundPage;
