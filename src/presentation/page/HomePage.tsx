import React, { useState } from 'react';
import { useResponsive } from '../../hooks/useResponsive';
import { Product } from '../../types';

interface HomePageProps {
  onProductClick?: (product: Product) => void;
  onSearchClick?: () => void;
}

const CATEGORIES = [
  { id: 'foods', name: 'Foods', icon: '🍔' },
  { id: 'drinks', name: 'Drinks', icon: '🥤' },
  { id: 'snacks', name: 'Snacks', icon: '🍿' },
  { id: 'sauce', name: 'Sauce', icon: '🍯' },
];

const SAMPLE_PRODUCTS: Product[] = [
  {
    id: '1',
    name: 'Veggie tomato mix',
    price: 1900,
    image: '/api/placeholder/150/150',
    description: 'Fresh vegetables mixed with tomatoes',
    category: 'foods'
  },
  {
    id: '2',
    name: 'Spicy fresh crab',
    price: 2350,
    image: '/api/placeholder/150/150',
    description: 'Delicious spicy crab with fresh herbs',
    category: 'foods'
  },
  {
    id: '3',
    name: 'Fresh Juice',
    price: 1200,
    image: '/api/placeholder/150/150',
    description: 'Freshly squeezed fruit juice',
    category: 'drinks'
  },
  {
    id: '4',
    name: 'Mixed Snacks',
    price: 850,
    image: '/api/placeholder/150/150',
    description: 'Assorted healthy snacks',
    category: 'snacks'
  }
];

const HomePage: React.FC<HomePageProps> = ({ onProductClick, onSearchClick }) => {
  const { isMobile } = useResponsive();
  const [selectedCategory, setSelectedCategory] = useState('foods');

  const filteredProducts = SAMPLE_PRODUCTS.filter(
    product => product.category === selectedCategory
  );

  return (
    <div className="screen-container bg-gray-50">
      {/* Header */}
      <div className="safe-area-top bg-white">
        <div className="px-6 py-4">
          {/* Menu icon and greeting */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <button className="p-2">
                <div className="space-y-1">
                  <div className="w-6 h-0.5 bg-gray-800"></div>
                  <div className="w-6 h-0.5 bg-gray-800"></div>
                  <div className="w-6 h-0.5 bg-gray-800"></div>
                </div>
              </button>
            </div>
          </div>

          {/* Main heading */}
          <div className="mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              Delicious
            </h1>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
              food to you
            </h2>
          </div>

          {/* Search bar */}
          <button
            onClick={onSearchClick}
            className="w-full bg-gray-100 rounded-3xl px-6 py-4 flex items-center space-x-3 text-left"
          >
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <span className="text-gray-500 flex-1">Search</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-6">
        {/* Categories */}
        <div className="mb-8">
          <div className="flex space-x-6 overflow-x-auto pb-2">
            {CATEGORIES.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`flex-shrink-0 text-center min-w-[80px] ${
                  selectedCategory === category.id
                    ? 'text-red-primary'
                    : 'text-gray-600'
                }`}
              >
                <div className="text-2xl mb-2">{category.icon}</div>
                <div className={`text-sm font-medium ${
                  selectedCategory === category.id
                    ? 'text-red-primary border-b-2 border-red-primary pb-1'
                    : 'text-gray-600'
                }`}>
                  {category.name}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-2 gap-4">
          {filteredProducts.map((product) => (
            <button
              key={product.id}
              onClick={() => onProductClick?.(product)}
              className="card hover:shadow-lg transition-shadow duration-200 text-left"
            >
              {/* Product Image */}
              <div className="aspect-square bg-gray-100 rounded-xl mb-3 overflow-hidden">
                <div className="w-full h-full bg-gradient-to-br from-orange-200 to-red-200 flex items-center justify-center">
                  <span className="text-4xl">🍽️</span>
                </div>
              </div>

              {/* Product Info */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                  {product.name}
                </h3>
                <p className="text-red-primary font-bold text-lg">
                  ₦{product.price.toLocaleString()}
                </p>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom" />
    </div>
  );
};

export default HomePage;
