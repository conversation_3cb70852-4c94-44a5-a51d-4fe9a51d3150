import React, { useState, useEffect } from 'react';
import { useResponsive } from '../../hooks/useResponsive';
import { Product } from '../../types';

interface SearchPageProps {
  onBack?: () => void;
  onProductClick?: (product: Product) => void;
  initialQuery?: string;
}

const SAMPLE_PRODUCTS: Product[] = [
  {
    id: '1',
    name: 'Veggie tomato mix',
    price: 1900,
    image: '/api/placeholder/150/150',
    description: 'Fresh vegetables mixed with tomatoes',
    category: 'foods'
  },
  {
    id: '2',
    name: 'Veggie tomato mix',
    price: 1900,
    image: '/api/placeholder/150/150',
    description: 'Fresh vegetables mixed with tomatoes',
    category: 'foods'
  },
  {
    id: '3',
    name: 'Spicy fresh crab',
    price: 2350,
    image: '/api/placeholder/150/150',
    description: 'Delicious spicy crab with fresh herbs',
    category: 'foods'
  },
  {
    id: '4',
    name: 'Mixed vegetables',
    price: 1500,
    image: '/api/placeholder/150/150',
    description: 'Assorted fresh vegetables',
    category: 'foods'
  }
];

const SearchPage: React.FC<SearchPageProps> = ({
  onBack,
  onProductClick,
  initialQuery = ''
}) => {
  const { isMobile } = useResponsive();
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (searchQuery.trim()) {
      setIsSearching(true);
      // Simulate search delay
      const timer = setTimeout(() => {
        const results = SAMPLE_PRODUCTS.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setSearchResults(results);
        setIsSearching(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [searchQuery]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by useEffect
  };

  return (
    <div className="screen-container bg-gray-200">
      {/* Header */}
      <div className="safe-area-top bg-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center space-x-4 mb-6">
            <button 
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-300 transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Search Input */}
            <form onSubmit={handleSearch} className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search"
                  className="w-full bg-white rounded-3xl px-6 py-4 pr-12 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-primary/20"
                  autoFocus
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 bg-gray-50 rounded-t-3xl px-6 py-6">
        {/* Results Header */}
        {searchQuery && (
          <div className="mb-6">
            <h2 className="text-lg font-bold text-gray-900">
              {isSearching ? 'Searching...' : `Found results`}
            </h2>
            {!isSearching && searchResults.length > 0 && (
              <p className="text-gray-600 text-sm mt-1">
                {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} for "{searchQuery}"
              </p>
            )}
          </div>
        )}

        {/* Search Results */}
        {isSearching ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-primary"></div>
          </div>
        ) : searchResults.length > 0 ? (
          <div className="grid grid-cols-2 gap-4">
            {searchResults.map((product) => (
              <button
                key={product.id}
                onClick={() => onProductClick?.(product)}
                className="card hover:shadow-lg transition-shadow duration-200 text-left"
              >
                {/* Product Image */}
                <div className="aspect-square bg-gray-100 rounded-xl mb-3 overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-orange-200 to-red-200 flex items-center justify-center">
                    <span className="text-4xl">🍅</span>
                  </div>
                </div>

                {/* Product Info */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1 text-sm">
                    {product.name}
                  </h3>
                  <p className="text-red-primary font-bold text-lg">
                    ₦{product.price.toLocaleString()}
                  </p>
                </div>
              </button>
            ))}
          </div>
        ) : searchQuery && !isSearching ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600 max-w-sm">
              We couldn't find any items matching "{searchQuery}". Try searching with different keywords.
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Search for food</h3>
            <p className="text-gray-600 max-w-sm">
              Enter keywords to find your favorite dishes and drinks.
            </p>
          </div>
        )}

        {/* Popular Searches */}
        {!searchQuery && (
          <div className="mt-8">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Popular searches</h3>
            <div className="flex flex-wrap gap-2">
              {['Pizza', 'Burger', 'Salad', 'Pasta', 'Sushi', 'Tacos'].map((term) => (
                <button
                  key={term}
                  onClick={() => setSearchQuery(term)}
                  className="px-4 py-2 bg-white rounded-full text-gray-700 text-sm font-medium hover:bg-gray-100 transition-colors duration-200"
                >
                  {term}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom bg-gray-50" />
    </div>
  );
};

export default SearchPage;
