/* OnboardingPage Custom CSS */

.onboarding-container {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  width: 100%;
  max-width: 414px;
  margin: 0 auto;
  background-color: #FF4B3A;
}

.onboarding-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.onboarding-circle {
  position: absolute;
  background-color: #FFFFFF;
  border-radius: 50%;
  top: 6.25%; /* 56/896 * 100 */
  left: 11.84%; /* 49/414 * 100 */
  width: 17.63%; /* 73/414 * 100 */
  aspect-ratio: 1;
}

.onboarding-person-1 {
  position: absolute;
  top: 49.27%; /* 441.46/896 * 100 */
  left: 47.58%; /* 196.94/414 * 100 */
  width: 64.58%; /* 267.36/414 * 100 */
  height: 36.69%; /* 328.79/896 * 100 */
}

.onboarding-person-2 {
  position: absolute;
  top: 38.73%; /* 347/896 * 100 */
  left: -19.81%; /* -82/414 * 100 */
  width: 92.03%; /* 381.04/414 * 100 */
  height: 50.56%; /* 453.07/896 * 100 */
}

.onboarding-person-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.onboarding-gradient-1 {
  position: absolute;
  top: 72.51%; /* 649.71/896 * 100 */
  left: 46.41%; /* 192.12/414 * 100 */
  width: 67.21%; /* 278.25/414 * 100 */
  height: 20.13%; /* 180.34/896 * 100 */
  background: linear-gradient(135deg, rgba(255, 71, 11, 0.51) 0%, rgba(255, 71, 11, 1) 100%);
  filter: blur(50px);
}

.onboarding-gradient-2 {
  position: absolute;
  top: 70.31%; /* 630/896 * 100 */
  left: -18.02%; /* -74.62/414 * 100 */
  width: 95.17%; /* 394/414 * 100 */
  height: 21.76%; /* 195/896 * 100 */
  background: linear-gradient(135deg, rgba(255, 71, 11, 0.1) 0%, rgba(255, 71, 11, 1) 100%);
  filter: blur(30px);
}

.onboarding-gradient-3 {
  position: absolute;
  top: 87.61%; /* 785/896 * 100 */
  left: 41.06%; /* 170/414 * 100 */
  width: 86.23%; /* 357/414 * 100 */
  height: 7.14%; /* 64/896 * 100 */
  background-color: #FF470B;
  filter: blur(30px);
}

.onboarding-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.onboarding-logo-container {
  position: absolute;
  top: 7.56%; /* 67.78/896 * 100 */
  left: 15.16%; /* 62.77/414 * 100 */
  width: 11.08%; /* 45.86/414 * 100 */
  height: 5.54%; /* 49.65/896 * 100 */
}

.onboarding-logo-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.onboarding-logo-main {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.onboarding-logo-vector {
  position: absolute;
  top: 22.68%; /* 11.26/49.65 * 100 */
  left: 60.11%; /* 27.56/45.86 * 100 */
  width: 39.93%; /* 18.31/45.86 * 100 */
  height: 71.33%; /* 35.41/49.65 * 100 */
}

.onboarding-heading-container {
  position: absolute;
  top: 17.86%; /* 160/896 * 100 */
  left: 12.32%; /* 51/414 * 100 */
  width: 66.43%; /* 275/414 * 100 */
  height: 12.5%; /* 112/896 * 100 */
  color: #FFFFFF;
}

.onboarding-heading {
  font-family: 'SF Pro Rounded', system-ui, -apple-system, sans-serif;
  font-weight: 800;
  font-size: 65px;
  line-height: 0.8683594336876502em;
  letter-spacing: -3%;
  text-align: left;
  margin: 0;
}

.onboarding-button-container {
  position: absolute;
  top: 88.17%; /* 790/896 * 100 */
  left: 12.32%; /* 51/414 * 100 */
  width: 75.85%; /* 314/414 * 100 */
  height: 7.81%; /* 70/896 * 100 */
}

.onboarding-button {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  border: none;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.onboarding-button:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.onboarding-button:active {
  transform: scale(0.95);
}

.onboarding-button-text {
  font-family: 'SF Pro Text', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  font-size: 17px;
  line-height: 1.193359375em;
  color: #FF460A;
  margin: 0;
}

/* Mobile-first responsive design */
@media (max-width: 375px) {
  .onboarding-heading {
    font-size: 55px;
  }

  .onboarding-button-text {
    font-size: 16px;
  }
}

@media (min-width: 415px) {
  .onboarding-container {
    max-width: 100%;
  }

  .onboarding-heading {
    font-size: 4rem;
  }

  .onboarding-button-text {
    font-size: 1.125rem;
  }
}

@media (min-width: 768px) {
  .onboarding-heading {
    font-size: 5rem;
  }
}

@media (min-width: 1024px) {
  .onboarding-heading {
    font-size: 6rem;
  }
}
