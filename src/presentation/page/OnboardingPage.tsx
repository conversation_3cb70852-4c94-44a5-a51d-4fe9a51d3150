import React from 'react';
import './OnboardingPage.css';

interface OnboardingPageProps {
  onGetStarted?: () => void;
}

const OnboardingPage: React.FC<OnboardingPageProps> = ({ onGetStarted }) => {
  return (
    <div className="onboarding-container">
      {/* Background decorative elements */}
      <div className="onboarding-background">
        {/* Top white circle - positioned as per Figma, responsive */}
        <div className="onboarding-circle" />

        {/* Person images - positioned as per Figma, responsive */}
        {/* Right person image */}
        <div className="onboarding-person-1">
          <img
            src={require('../../assets/images/person-1.png')}
            alt="Person 1"
            className="onboarding-person-image"
          />
        </div>

        {/* Left person image */}
        <div className="onboarding-person-2">
          <img
            src={require('../../assets/images/person-2.png')}
            alt="Person 2"
            className="onboarding-person-image"
          />
        </div>

        {/* Gradient overlays as per Figma - responsive */}
        <div className="onboarding-gradient-1" />
        <div className="onboarding-gradient-2" />
        <div className="onboarding-gradient-3" />
      </div>

      {/* Content */}
      <div className="onboarding-content">
        {/* Logo area - positioned as per Figma, responsive */}
        <div className="onboarding-logo-container">
          <div className="onboarding-logo-wrapper">
            <img
              src={require('../../assets/images/logo-168523.png')}
              alt="Logo"
              className="onboarding-logo-main"
            />
            <img
              src={require('../../assets/images/logo-vector.svg')}
              alt="Logo Vector"
              className="onboarding-logo-vector"
            />
          </div>
        </div>

        {/* Main heading - positioned as per Figma, responsive */}
        <div className="onboarding-heading-container">
          <h1 className="onboarding-heading">
            Food for<br />
            Everyone
          </h1>
        </div>

        {/* Bottom button - positioned as per Figma, responsive */}
        <div className="onboarding-button-container">
          <button
            onClick={onGetStarted}
            className="onboarding-button"
          >
            <span className="onboarding-button-text">
              Get starteed
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
