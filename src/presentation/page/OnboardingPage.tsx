import React from 'react';

interface OnboardingPageProps {
  onGetStarted?: () => void;
}

const OnboardingPage: React.FC<OnboardingPageProps> = ({ onGetStarted }) => {
  return (
    <div className="screen-container relative overflow-hidden min-h-screen w-full max-w-sm mx-auto" style={{ backgroundColor: '#FF4B3A' }}>
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        {/* Top white circle - positioned as per Figma, responsive */}
        <div
          className="absolute bg-white rounded-full"
          style={{
            top: '6.25%', // 56/896 * 100
            left: '11.84%', // 49/414 * 100
            width: '17.63%', // 73/414 * 100
            aspectRatio: '1'
          }}
        />

        {/* Person images - positioned as per Figma, responsive */}
        {/* Right person image */}
        <div
          className="absolute"
          style={{
            top: '49.27%', // 441.46/896 * 100
            left: '47.58%', // 196.94/414 * 100
            width: '64.58%', // 267.36/414 * 100
            height: '36.69%' // 328.79/896 * 100
          }}
        >
          <img
            src={require('../../assets/images/person-1.png')}
            alt="Person 1"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Left person image */}
        <div
          className="absolute"
          style={{
            top: '38.73%', // 347/896 * 100
            left: '-19.81%', // -82/414 * 100
            width: '92.03%', // 381.04/414 * 100
            height: '50.56%' // 453.07/896 * 100
          }}
        >
          <img
            src={require('../../assets/images/person-2.png')}
            alt="Person 2"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Gradient overlays as per Figma - responsive */}
        <div
          className="absolute"
          style={{
            top: '72.51%', // 649.71/896 * 100
            left: '46.41%', // 192.12/414 * 100
            width: '67.21%', // 278.25/414 * 100
            height: '20.13%', // 180.34/896 * 100
            background: 'linear-gradient(135deg, rgba(255, 71, 11, 0.51) 0%, rgba(255, 71, 11, 1) 100%)',
            filter: 'blur(50px)'
          }}
        />

        <div
          className="absolute"
          style={{
            top: '70.31%', // 630/896 * 100
            left: '-18.02%', // -74.62/414 * 100
            width: '95.17%', // 394/414 * 100
            height: '21.76%', // 195/896 * 100
            background: 'linear-gradient(135deg, rgba(255, 71, 11, 0.1) 0%, rgba(255, 71, 11, 1) 100%)',
            filter: 'blur(30px)'
          }}
        />

        <div
          className="absolute"
          style={{
            top: '87.61%', // 785/896 * 100
            left: '41.06%', // 170/414 * 100
            width: '86.23%', // 357/414 * 100
            height: '7.14%', // 64/896 * 100
            backgroundColor: '#FF470B',
            filter: 'blur(30px)'
          }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col h-full">
        {/* Logo area - positioned as per Figma, responsive */}
        <div
          className="absolute"
          style={{
            top: '7.56%', // 67.78/896 * 100
            left: '15.16%', // 62.77/414 * 100
            width: '11.08%', // 45.86/414 * 100
            height: '5.54%' // 49.65/896 * 100
          }}
        >
          <div className="relative w-full h-full">
            <img
              src={require('../../assets/images/logo-168523.png')}
              alt="Logo"
              className="w-full h-full object-contain"
            />
            <img
              src={require('../../assets/images/logo-vector.svg')}
              alt="Logo Vector"
              className="absolute"
              style={{
                top: '22.68%', // 11.26/49.65 * 100
                left: '60.11%', // 27.56/45.86 * 100
                width: '39.93%', // 18.31/45.86 * 100
                height: '71.33%' // 35.41/49.65 * 100
              }}
            />
          </div>
        </div>

        {/* Main heading - positioned as per Figma, responsive */}
        <div
          className="absolute text-white"
          style={{
            top: '17.86%', // 160/896 * 100
            left: '12.32%', // 51/414 * 100
            width: '66.43%', // 275/414 * 100
            height: '12.5%' // 112/896 * 100
          }}
        >
          <h1
            className="font-extrabold leading-none text-4xl sm:text-5xl md:text-6xl"
            style={{
              fontFamily: 'SF Pro Rounded, system-ui, -apple-system, sans-serif',
              fontWeight: 800,
              lineHeight: '0.87',
              letterSpacing: '-0.03em'
            }}
          >
            Food for<br />
            Everyone
          </h1>
        </div>

        {/* Bottom button - positioned as per Figma, responsive */}
        <div
          className="absolute"
          style={{
            top: '88.17%', // 790/896 * 100
            left: '12.32%', // 51/414 * 100
            width: '75.85%', // 314/414 * 100
            height: '7.81%' // 70/896 * 100
          }}
        >
          <button
            onClick={onGetStarted}
            className="w-full h-full bg-white flex items-center justify-center transition-all duration-200 active:scale-95 hover:shadow-lg"
            style={{ borderRadius: '30px' }}
          >
            <span
              className="font-semibold text-base sm:text-lg"
              style={{
                fontFamily: 'SF Pro Text, system-ui, -apple-system, sans-serif',
                fontWeight: 600,
                color: '#FF460A'
              }}
            >
              Get starteed
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;
