import React, { useState } from 'react';
import { useResponsive } from '../../hooks/useResponsive';
import { Product } from '../../types';

interface DetailPageProps {
  product?: Product;
  onBack?: () => void;
  onAddToCart?: (product: Product, quantity: number) => void;
}

const DEFAULT_PRODUCT: Product = {
  id: '1',
  name: 'Veggie tomato mix',
  price: 1900,
  image: '/api/placeholder/300/300',
  description: 'Shortbread, chocolate turtle cookies, and red velvet. 8 ounces of our best cookies.',
  category: 'foods'
};

const DetailPage: React.FC<DetailPageProps> = ({
  product = DEFAULT_PRODUCT,
  onBack,
  onAddToCart
}) => {
  const { isMobile } = useResponsive();
  const [quantity, setQuantity] = useState(1);

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    onAddToCart?.(product, quantity);
  };

  const totalPrice = product.price * quantity;

  return (
    <div className="screen-container bg-gray-50">
      {/* Header */}
      <div className="safe-area-top bg-gray-50">
        <div className="px-6 py-4">
          <button 
            onClick={onBack}
            className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 transition-colors duration-200"
          >
            <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Product Image */}
      <div className="px-6 mb-8">
        <div className="relative mx-auto w-80 h-80 max-w-full">
          <div className="w-full h-full rounded-full bg-gradient-to-br from-orange-200 to-red-200 flex items-center justify-center shadow-lg">
            <span className="text-8xl">🍅</span>
          </div>
        </div>
      </div>

      {/* Product Details */}
      <div className="flex-1 bg-white rounded-t-3xl px-6 py-8">
        {/* Product Name and Price */}
        <div className="mb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            {product.name}
          </h1>
          <p className="text-3xl font-bold text-red-primary">
            ₦{product.price.toLocaleString()}
          </p>
        </div>

        {/* Product Description */}
        <div className="mb-8">
          <p className="text-gray-600 leading-relaxed">
            {product.description}
          </p>
        </div>

        {/* Quantity and Add to Cart */}
        <div className="space-y-6">
          {/* Quantity Selector */}
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-900">Quantity</span>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleQuantityChange(-1)}
                disabled={quantity <= 1}
                className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 transition-colors duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              
              <span className="text-xl font-semibold text-gray-900 min-w-[2rem] text-center">
                {quantity}
              </span>
              
              <button
                onClick={() => handleQuantityChange(1)}
                className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center hover:bg-gray-200 transition-colors duration-200"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          </div>

          {/* Add to Cart Button */}
          <button
            onClick={handleAddToCart}
            className="w-full btn-primary text-center py-4 text-lg font-semibold"
          >
            Add to cart • ₦{totalPrice.toLocaleString()}
          </button>
        </div>
      </div>

      {/* Bottom safe area */}
      <div className="safe-area-bottom bg-white" />
    </div>
  );
};

export default DetailPage;
