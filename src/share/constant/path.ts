import { BLUE_PRINT_BASE_NAME, TEST_APP_BASE_NAME } from './router';

const isRunLocal = process.env.REACT_APP_ENV === 'local';
const appBaseName = isRunLocal ? TEST_APP_BASE_NAME : BLUE_PRINT_BASE_NAME;

export const PATH = {
  ONBOARDING: `${appBaseName}/onboarding`,
  HOME: `${appBaseName}/home`,
  DETAIL: `${appBaseName}/detail`,
  CART: `${appBaseName}/cart`,
  PAYMENT: `${appBaseName}/payment`,
  SEARCH: `${appBaseName}/search`,
  SEARCH_NOT_FOUND: `${appBaseName}/search-not-found`,
};
